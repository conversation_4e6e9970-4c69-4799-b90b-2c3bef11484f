.batch-record {
    max-width: 960px;
    margin: 40px auto;
    padding: 32px;
    background-color: #fefefe;
    border: 1px solid #ddd;
    border-radius: 12px;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
  }
  
  .header {
    text-align: center;
    margin-bottom: 32px;
    padding-bottom: 12px;
    border-bottom: 2px solid #ccc;
  }
  
  .header h1 {
    font-size: 22px;
    font-weight: 600;
    margin: 0;
    color: #2d2d2d;
  }
  
  .header h2 {
    font-size: 16px;
    margin-top: 8px;
    color: #444;
    font-weight: 500;
  }
  
  .section {
    margin-top: 32px;
  }
  
  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #444;
    border-bottom: 1px solid #e0e0e0;
    padding-bottom: 6px;
    margin-bottom: 16px;
  }
  
  .field-group {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px 20px;
  }
  
  .field {
    display: flex;
    flex-direction: column;
    padding: 6px 8px;
    background-color: #fafafa;
    border: 1px solid #e4e4e4;
    border-radius: 6px;
  }
  
  .field label {
    font-size: 13px;
    font-weight: 600;
    color: #666;
    margin-bottom: 4px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }
  
  .field span {
    font-size: 14px;
    color: #222;
    line-height: 1.4;
    white-space: pre-line;
  }
  
  @media (max-width: 768px) {
    .field-group {
      grid-template-columns: 1fr;
    }
  
    .header h1 {
      font-size: 18px;
    }
  
    .header h2 {
      font-size: 14px;
    }
  }
  