import React from "react";
import "./BatchRecord.css";

const data = {
  "BPR NO": "CP/DDDT303/MF/01/00/D",
  "SUPERSEDES NO": "NIL",
  "PRODUCT NAME": "ACILOC 300",
  "GENERIC NAME": "RANITIDINE TABLETS IP , 300 mg",
  "MARKETING DIVISION": "DOMESTIC (MAGFAM)",
  "MFG LICENSE NO": "G/1500",
  "COMPANY NAME": "CADILA PHARMACEUTICALS LIMITED",
  "MASTER PACKAGING DOCKET DETAILS": [
    {
      "PRODUCT NAME": "ACILOC 300",
      "PRODUCT CODE": "DT303",
      "MASTER REFERENCE NO": "DDT303/MF/01-00",
      "STANDARD BATCH SIZE": "2250000 TABLETS",
      "EXPIRY PERIOD": "30 MONTHS"
    }
  ],
  "BATCH PACKING RECORD DETAILS": [
    {
      "REASON FOR REVISION": "NEW BPR",
      "CHANGE CONTROL NO": "P/CC/FD/DOM/002/21",
      "EFFECTIVE DATE": "0 3 MAR 2021"
    }
  ],
  "BATCH PACKING DETAILS": [
    {
      "PACK STYLE": "20'S TABS / STRIP",
      "COUNTRY": "MAGFAM",
      "PROCESS ORDER NO": "10000197054",
      "COMMENCEMENT DATE": "26/07/21",
      "COMPLETION DATE": "28/07/21"
    }
  ],
  "BATCH DETAILS": [
    {
      "BATCH NO": "LO21076",
      "BPR ISSUED BY/DATE(QA)": "MAHI\n10/07/2021",
      "BATCH SIZE": "2250000",
      "MFG DATE": "JULY 2021",
      "EXP DATE": "DECEMBER 2023"
    }
  ]
};

const Row = ({ label, value }) => (
  <div className="field">
    <label>{label}</label>
    <span>{value}</span>
  </div>
);

const Section = ({ title, fields }) => (
  <div className="section">
    <div className="section-title">{title}</div>
    <div className="field-group">
      {fields.map(({ label, value }, index) => (
        <Row key={index} label={label} value={value} />
      ))}
    </div>
  </div>
);

export default function BatchRecordPage1() {
  const masterDetails = data["MASTER PACKAGING DOCKET DETAILS"][0];
  const recordDetails = data["BATCH PACKING RECORD DETAILS"][0];
  const packingDetails = data["BATCH PACKING DETAILS"][0];
  const batchDetails = data["BATCH DETAILS"][0];

  return (
    <div className="batch-record">
      <div className="header">
        <h1>{data["COMPANY NAME"]}</h1>
        <h2>BATCH PACKING RECORD (BPR)</h2>
      </div>

      <div className="field-group">
        <Row label="BPR NO" value={data["BPR NO"]} />
        <Row label="SUPERSEDES NO" value={data["SUPERSEDES NO"]} />
        <Row label="PRODUCT NAME" value={data["PRODUCT NAME"]} />
        <Row label="GENERIC NAME" value={data["GENERIC NAME"]} />
        <Row label="MARKETING DIVISION" value={data["MARKETING DIVISION"]} />
        <Row label="MFG LICENSE NO" value={data["MFG LICENSE NO"]} />
      </div>

      <Section
        title="MASTER PACKAGING DOCKET DETAILS"
        fields={Object.entries(masterDetails).map(([label, value]) => ({ label, value }))}
      />

      <Section
        title="BATCH PACKING RECORD DETAILS"
        fields={Object.entries(recordDetails).map(([label, value]) => ({ label, value }))}
      />

      <Section
        title="BATCH PACKING DETAILS"
        fields={Object.entries(packingDetails).map(([label, value]) => ({ label, value }))}
      />

      <Section
        title="BATCH DETAILS"
        fields={Object.entries(batchDetails).map(([label, value]) => ({ label, value }))}
      />
    </div>
  );
}